import { AuthRepository } from "../repositories/auth.repository";
import { UserRepository } from "../repositories/user.repository";
import { JwtUtils } from "../utils/jwt.utils";
import {
  CreateUserRequest,
  LoginRequest,
  LoginResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  ChangePasswordRequest,
  ResetPasswordRequest,
  User,
} from "../models/user.model";
import {
  CustomError,
  ValidationError,
  UnauthorizedError,
  ConflictError,
} from "../middleware/error.middleware";

export class AuthService {
  private authRepository: AuthRepository;
  private userRepository: UserRepository;

  constructor() {
    this.authRepository = new AuthRepository();
    this.userRepository = new UserRepository();
  }

  /**
   * Register a new user
   */
  async register(userData: CreateUserRequest): Promise<LoginResponse> {
    try {
      // Check if user already exists
      const existingUser = await this.userRepository.findByEmail(
        userData.email
      );
      if (existingUser) {
        throw new ConflictError("User with this email already exists");
      }

      // Validate password strength
      this.validatePassword(userData.password);

      // Register user with Supabase Auth
      const { user, session } = await this.authRepository.register(userData);

      // Generate our own JWT tokens
      const accessToken = JwtUtils.generateAccessToken(user);
      const refreshToken = JwtUtils.generateRefreshToken(user);

      return {
        user,
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_in: JwtUtils.getTokenExpirationTime(),
      };
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError(
        error instanceof Error ? error.message : "Registration failed",
        500
      );
    }
  }

  /**
   * Login user
   */
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      // Authenticate with Supabase
      const { user, session } = await this.authRepository.login(credentials);

      // Generate our own JWT tokens
      const accessToken = JwtUtils.generateAccessToken(user);
      const refreshToken = JwtUtils.generateRefreshToken(user);

      return {
        user,
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_in: JwtUtils.getTokenExpirationTime(),
      };
    } catch (error) {
      if (
        error instanceof Error &&
        error.message.includes("Invalid login credentials")
      ) {
        throw new UnauthorizedError("Invalid email or password");
      }
      throw new CustomError(
        error instanceof Error ? error.message : "Login failed",
        500
      );
    }
  }

  /**
   * Refresh access token
   */
  async refreshToken(
    request: RefreshTokenRequest
  ): Promise<RefreshTokenResponse> {
    try {
      // Verify refresh token
      const payload = JwtUtils.verifyToken(request.refresh_token);

      // Get user from database
      const user = await this.userRepository.findById(payload.sub);
      if (!user) {
        throw new UnauthorizedError("User not found");
      }

      // Generate new tokens
      const accessToken = JwtUtils.generateAccessToken(user);
      const refreshToken = JwtUtils.generateRefreshToken(user);

      return {
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_in: JwtUtils.getTokenExpirationTime(),
      };
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new UnauthorizedError("Invalid refresh token");
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      await this.authRepository.logout();
    } catch (error) {
      // Logout errors are usually not critical
      console.warn("Logout error:", error);
    }
  }

  /**
   * Reset password
   */
  async resetPassword(request: ResetPasswordRequest): Promise<void> {
    try {
      // Check if user exists
      const user = await this.userRepository.findByEmail(request.email);
      if (!user) {
        // Don't reveal if user exists or not for security
        return;
      }

      await this.authRepository.resetPassword(request.email);
    } catch (error) {
      throw new CustomError(
        error instanceof Error ? error.message : "Password reset failed",
        500
      );
    }
  }

  /**
   * Change password
   */
  async changePassword(
    userId: string,
    request: ChangePasswordRequest
  ): Promise<void> {
    try {
      // Get user
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new UnauthorizedError("User not found");
      }

      // Validate new password
      this.validatePassword(request.new_password);

      // For now, we'll use Supabase's password update
      // In a real implementation, you might want to verify the current password first
      const accessToken = JwtUtils.generateAccessToken(user);
      await this.authRepository.updatePassword(
        accessToken,
        request.new_password
      );
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError(
        error instanceof Error ? error.message : "Password change failed",
        500
      );
    }
  }

  /**
   * Get user profile by token
   */
  async getProfile(userId: string): Promise<User> {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new UnauthorizedError("User not found");
      }
      return user;
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError(
        error instanceof Error ? error.message : "Failed to get user profile",
        500
      );
    }
  }

  /**
   * Validate password strength
   */
  private validatePassword(password: string): void {
    const errors: Record<string, string[]> = {};

    if (password.length < 8) {
      errors.password = ["Password must be at least 8 characters long"];
    }

    if (!/(?=.*[a-z])/.test(password)) {
      errors.password = [
        ...(errors.password || []),
        "Password must contain at least one lowercase letter",
      ];
    }

    if (!/(?=.*[A-Z])/.test(password)) {
      errors.password = [
        ...(errors.password || []),
        "Password must contain at least one uppercase letter",
      ];
    }

    if (!/(?=.*\d)/.test(password)) {
      errors.password = [
        ...(errors.password || []),
        "Password must contain at least one number",
      ];
    }

    if (Object.keys(errors).length > 0) {
      throw new ValidationError("Password validation failed", errors);
    }
  }
}
