import { BaseRepository } from "./base.repository";
import {
  User,
  CreateUserRequest,
  UpdateUserRequest,
} from "../models/user.model";

export class UserRepository extends BaseRepository {
  private readonly tableName = "users";

  /**
   * Find user by ID
   */
  async findById(id: string): Promise<User | null> {
    try {
      const { data, error } = await this.client
        .from(this.tableName)
        .select("*")
        .eq("id", id)
        .single();

      if (error && error.code === "PGRST116") {
        return null;
      }

      if (error) {
        this.handleError(error);
      }

      return data as User;
    } catch (error) {
      console.error("Error finding user by ID:", error);
      throw error;
    }
  }

  /**
   * Find user by email
   */
  async findByEmail(email: string): Promise<User | null> {
    try {
      const { data, error } = await this.client
        .from(this.tableName)
        .select("*")
        .eq("email", email)
        .single();

      if (error && error.code === "PGRST116") {
        return null;
      }

      if (error) {
        this.handleError(error);
      }

      return data as User;
    } catch (error) {
      console.error("Error finding user by email:", error);
      throw error;
    }
  }

  /**
   * Create a new user
   */
  async create(userData: CreateUserRequest): Promise<User> {
    try {
      const now = this.getCurrentTimestamp();

      const userToCreate = {
        email: userData.email,
        full_name: userData.full_name || null,
        created_at: now,
        updated_at: now,
      };

      return await this.executeSingleQuery<User>(
        this.client.from(this.tableName).insert(userToCreate).select("*"),
        "Failed to create user"
      );
    } catch (error) {
      console.error("Error creating user:", error);
      throw error;
    }
  }

  /**
   * Update user
   */
  async update(id: string, userData: UpdateUserRequest): Promise<User> {
    try {
      const updateData = {
        ...userData,
        updated_at: this.getCurrentTimestamp(),
      };

      return await this.executeSingleQuery<User>(
        this.client
          .from(this.tableName)
          .update(updateData)
          .eq("id", id)
          .select("*"),
        "Failed to update user"
      );
    } catch (error) {
      console.error("Error updating user:", error);
      throw error;
    }
  }

  /**
   * Delete user
   */
  async delete(id: string): Promise<void> {
    try {
      await this.executeQuery(
        this.client.from(this.tableName).delete().eq("id", id),
        "Failed to delete user"
      );
    } catch (error) {
      console.error("Error deleting user:", error);
      throw error;
    }
  }

  /**
   * Get all users with pagination
   */
  async findAll(
    page: number = 1,
    limit: number = 10
  ): Promise<{ users: User[]; total: number }> {
    try {
      const { data, count } = await this.executePaginatedQuery<User>(
        this.client.from(this.tableName),
        page,
        limit,
        "Failed to fetch users"
      );

      return { users: data, total: count };
    } catch (error) {
      console.error("Error fetching users:", error);
      throw error;
    }
  }

  /**
   * Check if user exists by email
   */
  async existsByEmail(email: string): Promise<boolean> {
    try {
      return await this.recordExists(this.tableName, "email", email);
    } catch (error) {
      console.error("Error checking if user exists by email:", error);
      throw error;
    }
  }

  /**
   * Check if user exists by ID
   */
  async existsById(id: string): Promise<boolean> {
    try {
      return await this.recordExists(this.tableName, "id", id);
    } catch (error) {
      console.error("Error checking if user exists by ID:", error);
      throw error;
    }
  }
}
