import { BaseRepository } from "./base.repository";
import { User, CreateUserRequest, LoginRequest } from "../models/user.model";

export class AuthRepository extends BaseRepository {
  /**
   * Register a new user using Supabase Auth
   */
  async register(
    userData: CreateUserRequest
  ): Promise<{ user: User; session: any }> {
    try {
      const { data, error } = await this.client.auth.signUp({
        email: userData.email,
        password: userData.password,
        options: {
          data: {
            full_name: userData.full_name || null,
          },
        },
      });

      if (error) {
        console.error("Registration error:", error);
        throw new Error(error.message);
      }

      if (!data.user) {
        throw new Error("User registration failed");
      }

      // Map Supabase user to our User model
      const user: User = {
        id: data.user.id,
        email: data.user.email!,
        full_name: data.user.user_metadata?.full_name || null,
        avatar_url: data.user.user_metadata?.avatar_url || null,
        created_at: data.user.created_at!,
        updated_at: data.user.updated_at!,
      };

      return { user, session: data.session };
    } catch (error) {
      console.error("Error in register:", error);
      throw error;
    }
  }

  /**
   * Login user using Supabase Auth
   */
  async login(
    credentials: LoginRequest
  ): Promise<{ user: User; session: any }> {
    try {
      const { data, error } = await this.client.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      if (error) {
        console.error("Login error:", error);
        throw new Error(error.message);
      }

      if (!data.user || !data.session) {
        throw new Error("Login failed");
      }

      // Map Supabase user to our User model
      const user: User = {
        id: data.user.id,
        email: data.user.email!,
        full_name: data.user.user_metadata?.full_name || null,
        avatar_url: data.user.user_metadata?.avatar_url || null,
        created_at: data.user.created_at!,
        updated_at: data.user.updated_at!,
      };

      return { user, session: data.session };
    } catch (error) {
      console.error("Error in login:", error);
      throw error;
    }
  }

  /**
   * Refresh access token
   */
  async refreshToken(refreshToken: string): Promise<{ session: any }> {
    try {
      const { data, error } = await this.client.auth.refreshSession({
        refresh_token: refreshToken,
      });

      if (error) {
        console.error("Token refresh error:", error);
        throw new Error(error.message);
      }

      if (!data.session) {
        throw new Error("Token refresh failed");
      }

      return { session: data.session };
    } catch (error) {
      console.error("Error in refreshToken:", error);
      throw error;
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      const { error } = await this.client.auth.signOut();

      if (error) {
        console.error("Logout error:", error);
        throw new Error(error.message);
      }
    } catch (error) {
      console.error("Error in logout:", error);
      throw error;
    }
  }

  /**
   * Get user by access token
   */
  async getUserByToken(accessToken: string): Promise<User | null> {
    try {
      const { data, error } = await this.client.auth.getUser(accessToken);

      if (error) {
        console.error("Get user by token error:", error);
        return null;
      }

      if (!data.user) {
        return null;
      }

      // Map Supabase user to our User model
      const user: User = {
        id: data.user.id,
        email: data.user.email!,
        full_name: data.user.user_metadata?.full_name || null,
        avatar_url: data.user.user_metadata?.avatar_url || null,
        created_at: data.user.created_at!,
        updated_at: data.user.updated_at!,
      };

      return user;
    } catch (error) {
      console.error("Error in getUserByToken:", error);
      return null;
    }
  }

  /**
   * Reset password
   */
  async resetPassword(email: string): Promise<void> {
    try {
      const { error } = await this.client.auth.resetPasswordForEmail(email, {
        redirectTo: `${process.env.FRONTEND_URL}/reset-password`,
      });

      if (error) {
        console.error("Reset password error:", error);
        throw new Error(error.message);
      }
    } catch (error) {
      console.error("Error in resetPassword:", error);
      throw error;
    }
  }

  /**
   * Update password
   */
  async updatePassword(
    accessToken: string,
    newPassword: string
  ): Promise<void> {
    try {
      // Set the session first
      await this.client.auth.setSession({
        access_token: accessToken,
        refresh_token: "", // We don't need refresh token for password update
      });

      const { error } = await this.client.auth.updateUser({
        password: newPassword,
      });

      if (error) {
        console.error("Update password error:", error);
        throw new Error(error.message);
      }
    } catch (error) {
      console.error("Error in updatePassword:", error);
      throw error;
    }
  }
}
