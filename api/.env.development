# Development Environment Variables
NODE_ENV=development
PORT=3001

# Supabase Configuration
SUPABASE_URL=https://iajycvybkkrwmhkompec.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlhanljdnlia2tyd21oa29tcGVjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNDU0NzksImV4cCI6MjA2MTkyMTQ3OX0.hexGPbcg2iMheWmQYpgLR-QWmXawPHgkdZ3gfQIrTGQ
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlhanljdnlia2tyd21oa29tcGVjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjM0NTQ3OSwiZXhwIjoyMDYxOTIxNDc5fQ.Is19UEdTE6Apkxv7xgSm0o-GIKxA1ozXSG-tJrZofKk

# JWT Configuration
JWT_SECRET=deepledger_jwt_secret_dev_2024_secure_key_change_in_production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Database Configuration
DATABASE_URL=postgresql://postgres:[YOUR-PASSWORD]@db.iajycvybkkrwmhkompec.supabase.co:5432/postgres

# CORS Configuration
CORS_ORIGIN=http://localhost:5175

# Logging
LOG_LEVEL=debug

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
