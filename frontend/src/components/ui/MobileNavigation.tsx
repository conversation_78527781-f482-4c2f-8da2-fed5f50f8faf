import {
  LayoutDashboard,
  MessageSquare,
  Receipt,
  FileText,
  Settings,
  User,
  X
} from 'lucide-react';
import { Button } from './Button';
import { useAppStore, type ViewType } from '../../stores/app-store';
import { useChatStore } from '../../stores/chat-store';
import { cn } from '../../utils/cn';

const navigationItems: Array<{ id: ViewType; label: string; icon: any }> = [
  { id: 'dashboard', label: 'Dashboard', icon: LayoutDashboard },
  { id: 'chat', label: 'AI Assistant', icon: MessageSquare },
  { id: 'transactions', label: 'Transactions', icon: Receipt },
  { id: 'reports', label: 'Reports', icon: FileText },
  { id: 'profile', label: 'Profile', icon: User },
  { id: 'settings', label: 'Settings', icon: Settings },
];

interface MobileNavigationProps {
  isOpen: boolean;
  onClose: () => void;
}

export function MobileNavigation({ isOpen, onClose }: MobileNavigationProps) {
  const { currentView, setCurrentView } = useAppStore();
  const { isConnected } = useChatStore();

  const handleNavigation = (view: ViewType) => {
    setCurrentView(view);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 z-40 lg:hidden"
        onClick={onClose}
      />
      
      {/* Mobile Navigation Panel */}
      <div className="fixed inset-y-0 left-0 w-80 bg-background border-r border-border z-50 lg:hidden transform transition-transform duration-300 ease-in-out">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-border">
          <h2 className="text-lg font-semibold text-foreground">Navigation</h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="p-2"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Navigation Items */}
        <nav className="p-4 space-y-2">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const isActive = currentView === item.id;

            return (
              <Button
                key={item.id}
                variant={isActive ? 'default' : 'ghost'}
                className={cn(
                  'w-full justify-start space-x-3 h-12 text-left',
                  isActive && 'shadow-sm'
                )}
                onClick={() => handleNavigation(item.id)}
              >
                <Icon className="w-5 h-5" />
                <span>{item.label}</span>
                {item.id === 'chat' && !isConnected && (
                  <div className="w-2 h-2 bg-destructive rounded-full animate-pulse ml-auto" />
                )}
              </Button>
            );
          })}
        </nav>

        {/* Footer */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-border">
          <div className="text-center">
            <p className="text-sm text-muted-foreground">DeepLedger</p>
            <p className="text-xs text-muted-foreground">AI Accounting Assistant</p>
          </div>
        </div>
      </div>
    </>
  );
}
