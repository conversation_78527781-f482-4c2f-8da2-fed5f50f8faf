import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart3, FileText, Calculator, Database, Zap, Brain } from 'lucide-react';
import { cn } from '../../utils/cn';

// Agent tools configuration
const agentTools = [
  { id: 'transactions', name: 'Transactions', icon: FileText, description: 'Record and manage transactions' },
  { id: 'accounts', name: 'Chart of Accounts', icon: BarChart3, description: 'Manage account structure' },
  { id: 'reports', name: 'Financial Reports', icon: FileText, description: 'Generate financial reports' },
  { id: 'reconciliation', name: 'Bank Reconciliation', icon: Calculator, description: 'Reconcile bank statements' },
  { id: 'analytics', name: 'Financial Analytics', icon: BarChart3, description: 'Analyze financial data' },
  { id: 'budgeting', name: 'Budget Planning', icon: Calculator, description: 'Create and manage budgets' },
  { id: 'invoicing', name: 'Invoicing', icon: FileText, description: 'Generate and manage invoices' },
  { id: 'payroll', name: 'Payroll Management', icon: Database, description: 'Process payroll and benefits' },
  { id: 'tax', name: 'Tax Preparation', icon: Calculator, description: 'Prepare tax documents' },
  { id: 'audit', name: 'Audit Trail', icon: Database, description: 'Track all financial changes' },
  { id: 'compliance', name: 'Compliance Check', icon: Settings, description: 'Ensure regulatory compliance' },
  { id: 'forecasting', name: 'Financial Forecasting', icon: Brain, description: 'Predict future financial trends' },
  { id: 'automation', name: 'Process Automation', icon: Zap, description: 'Automate routine tasks' },
];

export function AgentToolsSidebar() {
  return (
    <div className="bg-card border-r border-border flex flex-col w-80 h-full">
      {/* Sidebar Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center space-x-2">
          <Bot className="w-6 h-6 text-primary" />
          <div>
            <h2 className="text-lg font-semibold text-card-foreground">Agent Tools</h2>
          </div>
        </div>
      </div>

      {/* Agent Tools List */}
      <div className="flex-1 overflow-y-auto p-4 space-y-2">
        {agentTools.map((tool) => {
          const Icon = tool.icon;
          return (
            <div
              key={tool.id}
              className="group relative rounded-lg bg-muted/50 hover:bg-muted transition-all duration-200 cursor-pointer border border-border/50 hover:border-border hover:shadow-sm active:scale-95 p-3"
            >
              <div className="flex items-start space-x-3">
                <Icon className="text-muted-foreground group-hover:text-primary transition-colors duration-200 w-5 h-5 mt-0.5" />
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium text-card-foreground group-hover:text-foreground transition-colors">
                    {tool.name}
                  </h3>
                  <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                    {tool.description}
                  </p>
                  <div className="mt-2">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-yellow-500/20 text-yellow-600 dark:text-yellow-400 border border-yellow-500/30">
                      Coming Soon
                    </span>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-border">
        <div className="text-xs text-muted-foreground text-center">
          <p>More tools coming soon!</p>
          <p className="mt-1">Powered by AI</p>
        </div>
      </div>
    </div>
  );
}
