import { create } from 'zustand';
import type { User, Session } from '@supabase/supabase-js';
import { supabase, authService } from '../services/supabase';

interface AuthState {
  user: User | null;
  session: Session | null;
  loading: boolean;
  initialized: boolean;
}

interface AuthActions {
  signIn: (email: string, password: string) => Promise<{ error?: any }>;
  signUp: (email: string, password: string, userData?: { full_name?: string }) => Promise<{ error?: any }>;
  signOut: () => Promise<void>;
  updateProfile: (updates: { full_name?: string; avatar_url?: string }) => Promise<{ error?: any }>;
  resetPassword: (email: string) => Promise<{ error?: any }>;
  initialize: () => Promise<void>;
  setUser: (user: User | null) => void;
  setSession: (session: Session | null) => void;
  setLoading: (loading: boolean) => void;
}

type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>((set, get) => ({
  // Initial state
  user: null,
  session: null,
  loading: true,
  initialized: false,

  // Actions
  signIn: async (email: string, password: string) => {
    set({ loading: true });
    try {
      const { data, error } = await authService.signIn(email, password);
      if (error) {
        set({ loading: false });
        return { error };
      }
      // Session will be set by the auth listener
      set({ loading: false });
      return {};
    } catch (error) {
      set({ loading: false });
      return { error };
    }
  },

  signUp: async (email: string, password: string, userData?: { full_name?: string }) => {
    set({ loading: true });
    try {
      const { data, error } = await authService.signUp(email, password, userData);
      if (error) {
        set({ loading: false });
        return { error };
      }
      set({ loading: false });
      return {};
    } catch (error) {
      set({ loading: false });
      return { error };
    }
  },

  signOut: async () => {
    set({ loading: true });
    try {
      await authService.signOut();
      set({ user: null, session: null, loading: false });
    } catch (error) {
      console.error('Sign out error:', error);
      set({ loading: false });
    }
  },

  updateProfile: async (updates: { full_name?: string; avatar_url?: string }) => {
    set({ loading: true });
    try {
      const { data, error } = await authService.updateProfile(updates);
      if (error) {
        set({ loading: false });
        return { error };
      }
      // Update local user state
      if (data.user) {
        set({ user: data.user, loading: false });
      }
      return {};
    } catch (error) {
      set({ loading: false });
      return { error };
    }
  },

  resetPassword: async (email: string) => {
    try {
      const { data, error } = await authService.resetPassword(email);
      return { error };
    } catch (error) {
      return { error };
    }
  },

  initialize: async () => {
    try {
      // Get initial session
      const { session } = await authService.getSession();
      set({
        session,
        user: session?.user || null,
        loading: false,
        initialized: true
      });

      // Listen for auth changes
      supabase.auth.onAuthStateChange((event, session) => {
        set({
          session,
          user: session?.user || null,
          loading: false
        });
      });
    } catch (error) {
      console.error('Auth initialization error:', error);
      set({ loading: false, initialized: true });
    }
  },

  setUser: (user: User | null) => set({ user }),
  setSession: (session: Session | null) => set({ session }),
  setLoading: (loading: boolean) => set({ loading }),
}));
